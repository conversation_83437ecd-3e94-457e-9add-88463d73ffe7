import { ArtistModel } from "@/app/models/Artist";
import { YtMusicItemTypeEnum } from "@/app/enums/YtMusicItemType";
import { JC_Utils, JC_Utils_YtMusic } from "@/app/Utils";

export const YT_GetSubscriptions = async function (authorization: string, cookie: string) : Promise<ArtistModel[]> {

    const body = {
        "context": JC_Utils_YtMusic.ytMusicContext(),
        "browseId": "VLLM"
    };

    const res = (await (await fetch('https://music.youtube.com/youtubei/v1/browse?prettyPrint=false', {
        method: 'POST',
        headers: JC_Utils_YtMusic.ytMusicHeaders(authorization, cookie),
        body: JSON.stringify(body)
    })).json());

    let userId = JC_Utils.findKeyValue(res.contents, "innertubeCommand")?.browseEndpoint?.browseId;

    // Return
    return userId;
}



// Get continued items
const GetSubscriptionsContinuedItems = async function (continuation:any, authorization: string = '', cookie: string = ''):Promise<any> {

    const body = {
        "context": JC_Utils_YtMusic.ytMusicContext()
    };

    const cRes = (await (await fetch(`https://music.youtube.com/youtubei/v1/browse?ctoken=${continuation.nextContinuationData.continuation}&continuation=${continuation.nextContinuationData.continuation}&itct=${continuation.nextContinuationData.clickTrackingParams}&type=next&&prettyPrint=false`, {
        method: 'POST',
        headers: JC_Utils_YtMusic.ytMusicHeaders(authorization, cookie),
        body: JSON.stringify(body)
    })).json());

    let cItems = [];

    let cItemsData = cRes.continuationContents.musicShelfContinuation;
    cItems.push(...cItemsData.contents);

    for (var c of cItemsData.continuations ?? []) {
        cItems.push(...(await GetSubscriptionsContinuedItems(c, authorization, cookie)));
    };

    return cItems;

}